# SSID 智能身份核验管理平台 - 产品需求文档 (PRD)

> **文档状态:** 基线版本 (Baseline)
> **版本:** 2.0 (功能导向版)
> **最后更新:** 2025-08-17

---

### 文档修订历史

| 版本  | 日期         | 修订人 | 修订说明                                     |
| :---- | :----------- | :----- | :------------------------------------------- |
| 1.0   | 2025-08-17   | Gemini | 初始版本，基于页面原型创建。                   |
| 2.0   | 2025-08-17   | Gemini | 重构为功能导向，聚焦于用户故事和核心功能。   |

---

## 1. 概述

### 1.1 产品愿景
打造一个功能强大、自主可控的身份核验管理平台，使我们的金融客户能够**灵活配置业务流程**、**主动防范安全风险**、**高效排查定位问题**，并**通过数据驱动持续优化用户体验**，最终构建一个安全与便捷兼备的数字身份体系。

### 1.2 目标用户与核心价值

| 目标用户         | 核心诉求 (Jobs to be Done)                             | 平台提供的核心价值                       |
| :--------------- | :----------------------------------------------------- | :--------------------------------------- |
| **系统管理员**   | 我需要安全地管理谁能访问这个平台，以及他们能做什么。   | 提供精细化的用户与角色权限管理 (RBAC)。    |
| **运营分析师**   | 我需要知道业务运行得怎么样，并能快速调整策略应对变化。 | 提供实时的数据洞察和灵活的策略配置能力。   |
| **风险管控师**   | 我需要及时发现并阻止欺诈行为，保护公司和用户资产。     | 提供主动式的风险监控、预警与处置机制。     |
| **技术支持/开发** | 当出现问题时，我需要快速定位是哪个环节出了错。         | 提供端到端的、可追溯的详细审计日志。     |

---

## 2. 功能需求详述 (Features)

### Feature 1: 策略配置管理 (Policy Configuration)

> **【为什么?】** 不同的业务场景（如注册、登录、支付）对安全性和用户体验的要求不同。平台必须提供灵活的配置能力，以适应客户多变的业务需求，而无需依赖原厂进行代码开发。

#### 1.1 业务元素定义
*   **用户故事**: 作为一个运营人员，我希望能自定义业务的“渠道”（如手机银行App）和“场景”（如大额转账），以便为它们配置不同的核验规则。
*   **功能需求**:
    *   平台应支持用户创建、读取、更新和删除（CRUD）“渠道”和“场景”两个基本业务元素。

#### 1.2 核验流程策略化
*   **用户故事**: 作为一个运营人员，我希望能创建一个“核验策略”，在其中完整定义一次人脸核验的所有环节，然后将这个策略应用到特定的渠道和场景中。
*   **功能需求**:
    *   **策略创建与绑定**: 支持创建“核验策略”，并将其与一个或多个“渠道/场景”进行关联。
    *   **活体模式选择**: 在策略中，必须能选择本次核验采用的活体检测模式（如：静默活体、交互活体、炫彩活体）。
    *   **动作配置**: 如果选择交互活体，平台必须支持自定义动作序列、动作数量和超时时间。
    *   **阈值与参数配置**:
        *   必须支持配置核心算法的判定阈值（如：活体分数、1:1比对分数、Deepfake分数）。
        *   必须支持配置人脸质量控制的开关和参数（如：是否允许闭眼、张嘴、面部遮挡）。
    *   **数据返回配置**: 支持配置核验成功后，需要返回给客户业务服务端的数据内容（如：最佳人脸图、全过程视频）。
    *   **策略版本控制**: 对策略的任何修改都应产生一个新版本。平台必须支持查看历史版本、对比版本差异，并能一键回滚到任一历史版本。

---

### Feature 2: 数据洞察与分析 (Data Analytics)

> **【为什么?】** 管理者需要量化地了解业务的健康状况，发现潜在问题，并评估策略调整带来的效果。数据是做出正确决策的基础。

*   **用户故事**: 作为一个运营分析师，我想知道不同渠道和场景下的核验通过率怎么样，如果通过率低，我想知道主要是什么原因导致的。
*   **功能需求**:
    *   **多维度通过率统计**: 平台必须提供按时间、渠道、场景、App版本、操作系统等多维度组合查询核验的通过率、失败率和用户放弃率。
    *   **失败原因归因分析**: 必须对失败案例进行自动归类（如：非活体攻击、人脸质量差、超时等），并以图表形式展示各类原因的占比和数量。
    *   **性能监控**: 平台应统计并展示端到端流程中关键环节的耗时分布，帮助定位性能瓶颈。

---

### Feature 3: 风险发现与控制 (Risk Management)

> **【为什么?】** 身份核验是金融业务的第一道防线，必须具备主动识别和拦截欺诈攻击的能力，以应对日益复杂的攻击手段（如模拟器、代理IP）。

#### 3.1 风险规则引擎
*   **用户故事**: 作为一个风险管控师，我希望能根据我们业务发现的风险特征（例如，某个设备在1分钟内尝试了10次），灵活地创建拦截规则。
*   **功能需求**:
    *   平台必须提供一个可视化的规则引擎。
    *   规则必须支持基于多种风险因子（如：设备指纹、IP地址、地理位置、是否模拟器、请求频率等）进行布尔逻辑组合。
    *   对于触发的规则，必须支持配置不同的响应动作（如：仅告警、提升认证等级、直接拒绝、拉黑设备）。

#### 3.2 风险事件监控与处置
*   **用户故事**: 作为一个风险管控师，我需要一个集中的地方查看所有触发的风险警报，并对这些事件进行处理和标记。
*   **功能需求**:
    *   平台必须提供一个风险事件列表，实时展示所有触发的风险事件详情。
    *   必须支持对风险事件进行人工处置和状态标记（如：待处理、已确认风险、误报）。

---

### Feature 4: 审计与追溯 (Audit & Traceability)

> **【为什么?】** 当用户投诉或出现资损事件时，技术和客服人员需要一个强有力的工具来快速、完整地回溯当时发生了什么，以定位问题、划分责任。

*   **用户故事**: 当用户反馈核验失败时，我希望能通过用户ID快速找到那一次请求的全部细节，包括前端和后端的日志、当时的视频和照片，以判断是用户操作问题还是系统问题。
*   **功能需求**:
    *   **案例检索**: 必须支持通过多种关键信息（用户ID、请求流水号、时间范围等）快速定位到任一历史核验记录。
    *   **请求快照**: 对于每一次核验，平台必须完整记录其所有上下文信息，包括：
        *   **环境信息**: App版本、SDK版本、设备型号、操作系统、IP地址。
        *   **策略信息**: 命中的渠道、场景、所使用的策略及其版本。
        *   **模型信息**: 调用的算法模型及其版本。
    *   **统一事件时间轴**: 必须将前端SDK和后端服务的关键日志，按时间顺序整合成一个人类可读的事件流，清晰地呈现整个核验过程。
    *   **多媒体证据**: 必须关联并支持在线查看该次核验的最佳人脸图和全过程视频。

---

### Feature 5: 资源管理与分发 (Resource Distribution)

> **【为什么?】** 算法模型和客户端授权需要不断更新。一个灵活的云端分发系统可以实现热更新，避免客户端频繁发布新版本，提升迭代效率和响应速度。

*   **用户故事**: 当我们有了新的、性能更好的活体模型后，我希望能先让一小部分用户（例如1%）使用新模型，观察其稳定性，然后再逐步推广给所有用户。
*   **功能需求**:
    *   **模型库管理**: 支持上传、版本化管理各类算法模型文件。
    *   **授权文件管理**: 支持客户上传由我方提供的SDK授权License文件进行统一管理。
    *   **动态下发策略**:
        *   必须支持创建下发任务，将指定的“模型”和“授权文件”分发给客户端。
        *   下发规则必须支持精细化配置，包括但不限于App版本、操作系统、渠道、用户分组等。
        *   必须支持灰度发布能力（按设备ID列表或用户百分比）。

---

### Feature 6: 1:N 特征库管理 (Feature Set Management)

> **【为什么?】** 对于需要进行人脸搜索或识别的业务（如黑名单布控、VIP识别），平台需要提供一套稳定、高效的人脸特征库管理工具。

*   **用户故事**: 作为一个业务管理员，我需要能方便地将新员工的照片录入到“员工人脸库”中，或者将某个已离职的员工从中删除。
*   **功能需求**:
    *   **库管理**: 支持创建、重命名、删除相互隔离的人脸特征库。
    *   **特征管理**: 必须支持对指定特征库中的人脸特征进行完整的增、删、改、查操作。
    *   **批量操作**: 必须支持批量导入和导出人脸特征数据。

---

## 4. 非功能性需求

*   **4.1 性能**: API核心接口响应时间P95应小于500ms。
*   **4.2 安全性**: 遵循最小权限原则，所有API需鉴权，防范常见Web攻击。
*   **4.3 可用性**: 保证核心服务99.9%的可用性。
*   **4.4 可维护性**: 提供完整的操作日志和系统监控能力。