<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSID 智能身份核验管理平台 - 首页仪表盘</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root { --bg-color: #f6f8fb; --sidebar-bg-color: #ffffff; --card-bg-color: #ffffff; --text-color: #1e293b; --text-color-secondary: #64748b; --border-color: #e5eaf3; --primary-color: #2d78f4; --primary-color-light: #f0f6ff; --shadow-color: rgba(0,0,0,0.06); --icon-color: #64748b; --icon-hover-bg: #f1f5f9; }
        [data-theme="dark"] { --bg-color: #111827; --sidebar-bg-color: #1f2937; --card-bg-color: #1f2937; --text-color: #f9fafb; --text-color-secondary: #9ca3af; --border-color: #374151; --primary-color: #3b82f6; --primary-color-light: #252e3d; --shadow-color: rgba(0,0,0,0.2); --icon-color: #9ca3af; --icon-hover-bg: #374151; }
        * { margin: 0; padding: 0; box-sizing: border-box; font-family: "PingFang SC", "Microsoft YaHei", sans-serif; }
        html, body { height: 100%; overflow: hidden; }
        body { background-color: var(--bg-color); color: var(--text-color); transition: background-color 0.3s, color 0.3s; display: flex; }
        .sidebar { width: 260px; background-color: var(--sidebar-bg-color); border-right: 1px solid var(--border-color); display: flex; flex-direction: column; height: 100%; flex-shrink: 0; transition: background-color 0.3s, border-color 0.3s; }
        .main-wrapper { flex-grow: 1; height: 100%; overflow-y: auto; }
        .main-content { padding: 24px; }
        .sidebar-header { display: flex; align-items: center; padding: 0 24px; height: 64px; border-bottom: 1px solid var(--border-color); flex-shrink: 0; }
        .sidebar-logo { font-size: 22px; font-weight: 700; color: var(--primary-color); }
        .sidebar-nav { flex-grow: 1; padding: 16px 0; }
        .nav-list { list-style: none; }
        .nav-item a { display: flex; align-items: center; gap: 12px; padding: 12px 24px; margin: 4px 16px; border-radius: 8px; color: var(--text-color-secondary); text-decoration: none; font-weight: 500; transition: background-color 0.2s, color 0.2s; }
        .nav-item a:hover { background-color: var(--primary-color-light); color: var(--primary-color); }
        .nav-item.active a { background-color: var(--primary-color-light); color: var(--primary-color); font-weight: 600; }
        .nav-icon { width: 20px; height: 20px; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
        .page-title { font-size: 24px; font-weight: 700; }
        .header-actions { display: flex; align-items: center; gap: 16px; }
        .theme-toggle { background-color: var(--icon-hover-bg); border: none; border-radius: 50%; width: 36px; height: 36px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: var(--icon-color); transition: background-color 0.2s; }
        .theme-toggle .icon { width: 20px; height: 20px; }
        #moon-icon { display: none; }
        .card { background-color: var(--card-bg-color); border-radius: 12px; margin-bottom: 24px; box-shadow: 0 4px 12px var(--shadow-color); border: 1px solid var(--border-color); overflow: hidden; transition: background-color 0.3s, border-color 0.3s; }
        .card-header { display: flex; justify-content: space-between; align-items: center; padding: 16px 24px; border-bottom: 1px solid var(--border-color); }
        .card-title { font-size: 17px; font-weight: 600; color: var(--text-color); }
        .card-content { padding: 24px; }
        .dashboard-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 24px; }
        .kpi-card .card-content { padding: 20px 24px; }
        .kpi-card .kpi-title { font-size: 14px; color: var(--text-color-secondary); margin-bottom: 8px; }
        .kpi-card .kpi-value { font-size: 32px; font-weight: 700; color: var(--text-color); line-height: 1.2; }
        .kpi-card .kpi-trend { display: flex; align-items: center; font-size: 13px; margin-top: 8px; color: var(--text-color-secondary); }
        .kpi-trend .trend-icon { margin-right: 4px; font-weight: bold; }
        .kpi-trend .trend-up { color: #4cd964; }
        .kpi-trend .trend-down { color: #ff3b30; }
        .chart-container { position: relative; height: 300px; width: 100%; }
        .failure-chart-layout { display: flex; align-items: center; gap: 30px; }
        .failure-chart-wrapper { position: relative; width: 180px; height: 180px; flex-shrink: 0; }
        .failure-stats { flex-grow: 1; }
        .failure-stat-item { display: flex; justify-content: space-between; align-items: center; padding: 8px 0; font-size: 14px; border-bottom: 1px solid var(--border-color); }
        .failure-stat-item:last-child { border-bottom: none; }
        .stat-label { display: flex; align-items: center; gap: 8px; color: var(--text-color); }
        .stat-color-dot { width: 10px; height: 10px; border-radius: 50%; }
        .stat-value { font-weight: 600; color: var(--text-color); }
        .status-list { list-style: none; display: flex; flex-direction: column; gap: 16px; }
        .status-item { display: flex; justify-content: space-between; align-items: center; font-size: 15px; }
        .status-badge { display: flex; align-items: center; gap: 6px; font-weight: 500; }
        .status-indicator { width: 9px; height: 9px; border-radius: 50%; }
        .status-online { color: #34a853; } .status-online .status-indicator { background-color: #4cd964; }
        .status-offline { color: #ea4335; } .status-offline .status-indicator { background-color: #ff3b30; }
        .events-table { width: 100%; border-collapse: collapse; }
        .events-table th, .events-table td { text-align: left; padding: 10px 4px; font-size: 14px; border-bottom: 1px solid var(--border-color); }
        .events-table th { color: var(--text-color-secondary); font-weight: 500; }
        .events-table tr:last-child td { border-bottom: none; }
        .risk-badge { padding: 3px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .risk-high { background-color: rgba(255, 59, 48, 0.15); color: #ff3b30; }
        .risk-medium { background-color: rgba(255, 149, 0, 0.15); color: #ff9500; }
        @media (max-width: 1200px) { .dashboard-grid { grid-template-columns: repeat(2, 1fr); } }
        @media (max-width: 768px) { .dashboard-grid { grid-template-columns: 1fr; } .failure-chart-layout { flex-direction: column; } .sidebar { display: none; } .main-wrapper { width: 100%; } }
    </style>
</head>
<body data-theme="light">

    <!-- ########## CORRECTED SIDEBAR START ########## -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <h1 class="sidebar-logo">SSID Platform</h1>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item active">
                    <!-- 修正点: href 指向自身 -->
                    <a href="dashboard.html">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path></svg>
                        <span>首页仪表盘</span>
                    </a>
                </li>
                <li class="nav-item">
                    <!-- 修正点: href 指向 configuration.html -->
                    <a href="configuration.html">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>
                        <span>配置管理中心</span>
                    </a>
                </li>
                <li class="nav-item">
                    <!-- 修正点: href 指向 analytics.html -->
                    <a href="analytics.html">
                       <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>
                        <span>数据统计与分析</span>
                    </a>
                </li>
                <li class="nav-item">
                    <!-- 修正点: href 指向 risk-management.html -->
                    <a href="risk-management.html">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg>
                        <span>风险管控引擎</span>
                    </a>
                </li>
                 <li class="nav-item">
                    <!-- 修正点: href 指向 case-audit.html -->
                    <a href="case-audit.html">
                       <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>
                        <span>案例审计中心</span>
                    </a>
                </li>
                <li class="nav-item">
                    <!-- 修正点: href 指向 feature-sets.html -->
                    <a href="feature-sets.html">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path></svg>
                        <span>特征库管理</span>
                    </a>
                </li>
                 <li class="nav-item">
                    <!-- 修正点: href 指向 distribution.html -->
                    <a href="distribution.html">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path></svg>
                        <span>资源分发中心</span>
                    </a>
                </li>
                 <li class="nav-item">
                    <!-- 修正点: href 指向 settings.html -->
                    <a href="settings.html">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 16v-2m8-8h2M4 12H2m15.364 6.364l-1.414-1.414M6.05 6.05L4.636 4.636m12.728 12.728L15.95 15.95M6.05 17.95l1.414-1.414M12 18a6 6 0 100-12 6 6 0 000 12z"></path></svg>
                        <span>系统管理</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>
    <!-- ########## CORRECTED SIDEBAR END ########## -->

    <!-- Main Content -->
    <div class="main-wrapper">
        <main class="main-content">
            <header class="page-header">
                <h1 class="page-title">首页仪表盘</h1>
                <div class="header-actions">
                    <button class="theme-toggle" id="theme-toggle-btn" title="切换亮/暗模式">
                        <svg id="sun-icon" class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path></svg>
                        <svg id="moon-icon" class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path></svg>
                    </button>
                </div>
            </header>
            <div class="dashboard-grid">
                <div class="card kpi-card">
                    <div class="card-content">
                        <div class="kpi-title">今日核验总量</div>
                        <div class="kpi-value">18,765</div>
                        <div class="kpi-trend"><span class="trend-icon trend-up">↑</span><span class="trend-up">12.5%</span>&nbsp;较昨日</div>
                    </div>
                </div>
                <div class="card kpi-card">
                    <div class="card-content">
                        <div class="kpi-title">实时核验通过率</div>
                        <div class="kpi-value">96.8%</div>
                        <div class="kpi-trend"><span class="trend-icon trend-down">↓</span><span class="trend-down">0.2%</span>&nbsp;较昨日</div>
                    </div>
                </div>
                <div class="card kpi-card">
                    <div class="card-content">
                        <div class="kpi-title">今日新增风险事件</div>
                        <div class="kpi-value">42</div>
                        <div class="kpi-trend"><span style="color: #ff9500;">中风险: 35</span>, <span style="color: #ff3b30;">高风险: 7</span></div>
                    </div>
                </div>
                <div class="card kpi-card">
                    <div class="card-content">
                        <div class="kpi-title">平均核验时长</div>
                        <div class="kpi-value">5.8s</div>
                        <div class="kpi-trend"><span class="trend-icon trend-up" style="color: #4cd964;">↓</span><span style="color: #4cd964;">0.3s</span><span>&nbsp;较昨日</span></div>
                    </div>
                </div>
                <div class="card" style="grid-column: 1 / 3;">
                    <div class="card-header"><div class="card-title">核验量趋势分析</div></div>
                    <div class="card-content"><div class="chart-container"><canvas id="verificationTrendChart"></canvas></div></div>
                </div>
                <div class="card" style="grid-column: 3 / 5;">
                    <div class="card-header"><div class="card-title">核验失败原因分布</div></div>
                    <div class="card-content">
                        <div class="failure-chart-layout">
                            <div class="failure-chart-wrapper"><canvas id="failureReasonChart"></canvas></div>
                            <div class="failure-stats">
                                <div class="failure-stat-item"><div class="stat-label"><div class="stat-color-dot" style="background-color: #ff6384;"></div><span>非活体攻击</span></div><div class="stat-value">38%</div></div>
                                <div class="failure-stat-item"><div class="stat-label"><div class="stat-color-dot" style="background-color: #ff9f40;"></div><span>人脸质量差</span></div><div class="stat-value">25%</div></div>
                                <div class="failure-stat-item"><div class="stat-label"><div class="stat-color-dot" style="background-color: #ffcd56;"></div><span>用户操作超时</span></div><div class="stat-value">21%</div></div>
                                <div class="failure-stat-item"><div class="stat-label"><div class="stat-color-dot" style="background-color: #4bc0c0;"></div><span>网络错误</span></div><div class="stat-value">11%</div></div>
                                <div class="failure-stat-item"><div class="stat-label"><div class="stat-color-dot" style="background-color: #9966ff;"></div><span>其他</span></div><div class="stat-value">5%</div></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card" style="grid-column: 1 / 3;">
                     <div class="card-header"><div class="card-title">系统核心服务状态</div></div>
                    <div class="card-content">
                        <ul class="status-list">
                            <li class="status-item"><span>活体检测服务</span><div class="status-badge status-online"><div class="status-indicator"></div><span>运行正常</span></div></li>
                            <li class="status-item"><span>人脸比对服务</span><div class="status-badge status-online"><div class="status-indicator"></div><span>运行正常</span></div></li>
                            <li class="status-item"><span>风险管控引擎</span><div class="status-badge status-online"><div class="status-indicator"></div><span>运行正常</span></div></li>
                            <li class="status-item"><span>管理后台服务</span><div class="status-badge status-offline"><div class="status-indicator"></div><span>节点异常</span></div></li>
                        </ul>
                    </div>
                </div>
                <div class="card" style="grid-column: 3 / 5;">
                     <div class="card-header"><div class="card-title">最新风险事件</div></div>
                    <div class="card-content" style="padding-top: 12px; padding-bottom: 12px;">
                        <table class="events-table">
                            <thead><tr><th>时间</th><th>用户ID</th><th>风险等级</th><th>触发规则</th></tr></thead>
                            <tbody>
                                <tr><td>10:58:12</td><td>user_11****34</td><td><span class="risk-badge risk-high">高风险</span></td><td>模拟器登录</td></tr>
                                <tr><td>10:57:45</td><td>user_9a****f1</td><td><span class="risk-badge risk-medium">中风险</span></td><td>设备高频请求</td></tr>
                                <tr><td>10:56:02</td><td>user_c3****88</td><td><span class="risk-badge risk-medium">中风险</span></td><td>IP位于风险地区</td></tr>
                                <tr><td>10:55:31</td><td>user_11****34</td><td><span class="risk-badge risk-high">高风险</span></td><td>设备已被拉黑</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
    <script>
        const themeToggleBtn = document.getElementById('theme-toggle-btn');
        const sunIcon = document.getElementById('sun-icon');
        const moonIcon = document.getElementById('moon-icon');
        const body = document.body;
        const applyTheme = (theme) => {
            body.setAttribute('data-theme', theme);
            if (theme === 'dark') { sunIcon.style.display = 'none'; moonIcon.style.display = 'block'; } else { sunIcon.style.display = 'block'; moonIcon.style.display = 'none'; }
            if (window.charts && window.charts.length > 0) { window.charts.forEach(chart => chart.destroy()); }
            setTimeout(() => initializeCharts(theme), 0);
        };
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        const defaultTheme = savedTheme || (prefersDark ? 'dark' : 'light');
        applyTheme(defaultTheme);
        themeToggleBtn.addEventListener('click', () => {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            localStorage.setItem('theme', newTheme);
            applyTheme(newTheme);
        });
        window.charts = [];
        function initializeCharts(theme) {
            const isDark = theme === 'dark';
            const gridColor = isDark ? 'rgba(255, 255, 255, 0.1)' : '#f0f3f7';
            const textColor = isDark ? '#9ca3af' : '#64748b';
            const primaryColor = isDark ? '#3b82f6' : '#2d78f4';
            const primaryBgColor = isDark ? 'rgba(59, 130, 246, 0.2)' : 'rgba(45, 120, 244, 0.1)';
            const trendCtx = document.getElementById('verificationTrendChart').getContext('2d');
            const trendChart = new Chart(trendCtx, { type: 'line', data: { labels: ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00'], datasets: [{ label: '核验次数', data: [120, 150, 800, 2500, 4800, 3200, 5100, 2900], backgroundColor: primaryBgColor, borderColor: primaryColor, borderWidth: 2, fill: true, tension: 0.4, pointBackgroundColor: isDark ? '#1f2937' : '#fff', pointBorderColor: primaryColor, pointHoverRadius: 6 }] }, options: { responsive: true, maintainAspectRatio: false, scales: { y: { beginAtZero: true, grid: { color: gridColor }, ticks: { color: textColor } }, x: { grid: { display: false }, ticks: { color: textColor } } }, plugins: { legend: { display: false } } } });
            window.charts.push(trendChart);
            const failureCtx = document.getElementById('failureReasonChart').getContext('2d');
            const failureChart = new Chart(failureCtx, { type: 'doughnut', data: { labels: ['非活体攻击', '人脸质量差', '用户操作超时', '网络错误', '其他'], datasets: [{ data: [38, 25, 21, 11, 5], backgroundColor: ['#ff6384', '#ff9f40', '#ffcd56', '#4bc0c0', '#9966ff'], borderWidth: 0, hoverOffset: 8 }] }, options: { responsive: true, maintainAspectRatio: false, cutout: '70%', plugins: { legend: { display: false }, tooltip: { callbacks: { label: (context) => context.label + ': ' + context.raw + '%' } } } } });
            window.charts.push(failureChart);
        }
    </script>
</body>
</html>